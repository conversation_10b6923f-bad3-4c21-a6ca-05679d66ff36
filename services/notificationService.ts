import { Platform, Alert } from 'react-native';
import * as Haptics from 'expo-haptics';
import * as Device from 'expo-device';

export interface BlockingNotification {
  id: string;
  appName: string;
  message: string;
  timestamp: Date;
  type: 'blocked' | 'warning' | 'success';
}

class NotificationService {
  private notifications: BlockingNotification[] = [];
  private listeners: Array<(notifications: BlockingNotification[]) => void> = [];

  // Add a listener for notification updates
  addListener(callback: (notifications: BlockingNotification[]) => void) {
    this.listeners.push(callback);
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }

  // Notify all listeners of notification changes
  private notifyListeners() {
    this.listeners.forEach(listener => listener([...this.notifications]));
  }

  // Show a blocking notification
  async showBlockingNotification(
    appName: string,
    blockReason: 'focus_session' | 'break_time' | 'custom_schedule' | 'manual_block'
  ) {
    const messages = {
      focus_session: `${appName} is blocked during focus sessions. Stay focused! 🎯`,
      break_time: `${appName} is blocked during breaks. Take a real break! ☕`,
      custom_schedule: `${appName} is blocked according to your schedule. 📅`,
      manual_block: `${appName} is currently blocked. 🚫`,
    };

    const notification: BlockingNotification = {
      id: Date.now().toString(),
      appName,
      message: messages[blockReason],
      timestamp: new Date(),
      type: 'blocked',
    };

    this.notifications.unshift(notification);
    
    // Keep only the last 10 notifications
    if (this.notifications.length > 10) {
      this.notifications = this.notifications.slice(0, 10);
    }

    this.notifyListeners();

    // Provide haptic feedback
    if (Device.isDevice && Platform.OS !== 'web') {
      try {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      } catch (error) {
        console.warn('Haptic feedback error:', error);
      }
    }

    // Show system alert for important blocks
    if (Platform.OS === 'web') {
      this.showWebNotification(notification);
    }

    return notification;
  }

  // Show a success notification
  async showSuccessNotification(message: string) {
    const notification: BlockingNotification = {
      id: Date.now().toString(),
      appName: 'System',
      message,
      timestamp: new Date(),
      type: 'success',
    };

    this.notifications.unshift(notification);
    
    if (this.notifications.length > 10) {
      this.notifications = this.notifications.slice(0, 10);
    }

    this.notifyListeners();

    // Provide haptic feedback
    if (Device.isDevice && Platform.OS !== 'web') {
      try {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } catch (error) {
        console.warn('Haptic feedback error:', error);
      }
    }

    return notification;
  }

  // Show a warning notification
  async showWarningNotification(message: string) {
    const notification: BlockingNotification = {
      id: Date.now().toString(),
      appName: 'System',
      message,
      timestamp: new Date(),
      type: 'warning',
    };

    this.notifications.unshift(notification);
    
    if (this.notifications.length > 10) {
      this.notifications = this.notifications.slice(0, 10);
    }

    this.notifyListeners();

    // Provide haptic feedback
    if (Device.isDevice && Platform.OS !== 'web') {
      try {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
      } catch (error) {
        console.warn('Haptic feedback error:', error);
      }
    }

    return notification;
  }

  // Show web notification (for browser)
  private showWebNotification(notification: BlockingNotification) {
    if ('Notification' in window) {
      if (Notification.permission === 'granted') {
        new Notification('Distraction Blocked', {
          body: notification.message,
          icon: '/favicon.ico',
          badge: '/favicon.ico',
        });
      } else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            new Notification('Distraction Blocked', {
              body: notification.message,
              icon: '/favicon.ico',
              badge: '/favicon.ico',
            });
          }
        });
      }
    }
  }

  // Show emergency access dialog
  async showEmergencyAccessDialog(appName: string): Promise<boolean> {
    return new Promise((resolve) => {
      Alert.alert(
        'Emergency Access',
        `Do you really need to access ${appName} right now? This will temporarily disable blocking for 5 minutes.`,
        [
          {
            text: 'Stay Focused',
            style: 'cancel',
            onPress: () => resolve(false),
          },
          {
            text: 'Emergency Access',
            style: 'destructive',
            onPress: () => resolve(true),
          },
        ],
        { cancelable: true, onDismiss: () => resolve(false) }
      );
    });
  }

  // Show focus session complete notification
  async showFocusCompleteNotification(duration: number) {
    const minutes = Math.floor(duration / 60);
    const message = `🎉 Focus session complete! You focused for ${minutes} minutes. Great job!`;
    
    await this.showSuccessNotification(message);
    
    // Extra celebration haptic
    if (Device.isDevice && Platform.OS !== 'web') {
      setTimeout(async () => {
        try {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        } catch (error) {
          console.warn('Haptic feedback error:', error);
        }
      }, 100);
      setTimeout(async () => {
        try {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        } catch (error) {
          console.warn('Haptic feedback error:', error);
        }
      }, 200);
    }
  }

  // Show break reminder
  async showBreakReminder() {
    const message = '☕ Time for a break! Step away from your screen and recharge.';
    await this.showSuccessNotification(message);
  }

  // Get all notifications
  getNotifications(): BlockingNotification[] {
    return [...this.notifications];
  }

  // Clear all notifications
  clearNotifications() {
    this.notifications = [];
    this.notifyListeners();
  }

  // Remove a specific notification
  removeNotification(id: string) {
    this.notifications = this.notifications.filter(n => n.id !== id);
    this.notifyListeners();
  }

  // Simulate app access attempt (for demo purposes)
  async simulateAppAccessAttempt(appName: string, isBlocked: boolean) {
    if (isBlocked) {
      await this.showBlockingNotification(appName, 'focus_session');
    } else {
      await this.showWarningNotification(`${appName} access allowed - stay mindful! 🧘`);
    }
  }
}

export const notificationService = new NotificationService();
